<popup>
  <view class="record-pop-bk">
    <view class="recordTitle" tt:if="{{$store.recordList.length > 0}}">
      <view class="name">礼品名称</view>
      <view class="time">礼品时间</view>
      <view class="btn">状态</view>
    </view>
    <scroll-view class="scroll-content" scroll-y="{{true}}" tt:if="{{$store.recordList.length > 0}}">
      <view>
        <view tt:for="{{$store.recordList}}" class="row">
          <view class="name">{{item.prizeName}}</view>
          <view class="time">{{item.createTime}}</view>
          <image tt:if="{{item.hasFinished}}" class="btn" bindtap="handleOpenAddress" data-item="{{item}}" mode="widthFix" src="//img10.360buyimg.com/imgzone/jfs/t1/317092/5/14511/7760/686cf950F114f2581/a84537bda221827b.png" />
          <image tt:else class="btn" bindtap="handleOpenAddress" data-item="{{item}}" mode="widthFix" src="//img10.360buyimg.com/imgzone/jfs/t1/304046/13/16224/15624/686cf950F625e3080/290a76dba5279ed9.png" />
        </view>
      </view>
    </scroll-view>
    <view class="no-data" tt:else>
      <image bindtap="handleOpenAddress" src="//img10.360buyimg.com/imgzone/jfs/t1/316793/10/13583/24920/68674e5eF28fa5b9f/0e713a141d78302e.png" mode="widthFix"></image>
    </view>
    <view class="close" bindtap="onClose"></view>
  </view>
</popup>